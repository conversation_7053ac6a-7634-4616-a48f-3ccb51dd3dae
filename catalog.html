<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description"
    content="Entdecken Sie den IsoPur Online Produkt Katalog mit hochwertigen Abdichtungslösungen für Bauprojekte.">
  <meta name="keywords" content="IsoPur, Abdichtung, Bauprojekte, Produktkatalog, hochwertige Lösungen, Bauindustrie">
  <meta name="author" content="IsoPur Team">
  <title>Entdecken Sie den IsoPur Online Produkt Katalog | IsoPur GmbH</title>
  <link rel="apple-touch-icon" sizes="180x180" href="./assets/img/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="./assets/img/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="./assets/img/favicon-16x16.png">
  <link rel="icon" type="image/ico" sizes="original" href="./assets/img/favicon.ico">
  <link rel="manifest" href="./assets/img/site.webmanifest">
  <link rel="stylesheet" href="./assets/css/plugins.css">
  <link rel="stylesheet" href="./assets/css/style.css">
  <link rel="stylesheet" href="./assets/css/custom.css">

</head>

<body>
  <div class="content-wrapper">
    <header class="wrapper bg-light">
      <nav class="navbar navbar-expand-lg center-nav navbar-dark navbar-bg-dark shadow-sm">
        <div class="container flex-lg-row flex-nowrap align-items-center">
          <div class="navbar-brand w-100">
            <a href="./index.html">
              <img class="logo-light " src="./assets/img/logo-no_slogan.png"
                srcset="./assets/img/<EMAIL> 2x" alt="ISOPUR Logo"
                style="max-width: 180px; width: auto; height: auto;" />
            </a>
          </div>
          <div class="navbar-collapse offcanvas offcanvas-nav offcanvas-start">
            <div class="navbar-brand w-100">
              <!-- <a href="./index.html">
              <img class="px-12 " src="./assets/img/logo-no_slogan.png" srcset="./assets/img/<EMAIL> 2x" alt="ISOPUR Logo" width="100%" />
        </a> -->
            </div>
            <div class="offcanvas-header d-lg-none justify-content-between">
              <h3 class="text-white fs-30 mb-0">ISOPUR<br /><span class="text-muted fs-15 mb-0">Kunststofftechnik
                  GmbH</span></h3>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>
            </div>
            <div class="offcanvas-body ms-lg-auto d-flex flex-column h-100">
              <ul class="navbar-nav">
                <li class="nav-item">
                  <a class="nav-link" href="/">Home</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="index.html#products">Neuheiten & Angebote</a>
                </li>
                <li class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Produkte</a>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="./catalog.html">Produktkatalog</a></li>
                    <li>
                      <hr class="dropdown-divider">
                    </li>
                    <li><a class="dropdown-item" href="products/bauwerksabdichtung.html">Bauwerksabdichtung</a></li>
                    <li><a class="dropdown-item" href="products/wdv-systeme.html">WDV-Systeme</a></li>
                    <li><a class="dropdown-item" href="products/farben-oberflachenschutz.html">Farben &
                        Oberflächenschutz</a></li>
                    <li><a class="dropdown-item" href="products/fliesen-natursteinverlegung.html">Fliesen- &
                        Natursteinverlegung</a></li>
                    <li><a class="dropdown-item" href="products/betonherstellung-instandsetzung.html">Betonherstellung &
                        -instandsetzung</a></li>
                    <li><a class="dropdown-item"
                        href="products/mauerwerksherstellung-instandsetzung.html">Mauerwerksherstellung &
                        -instandsetzung</a></li>
                    <li><a class="dropdown-item"
                        href="products/industriebeschichtungen.html">Industriebeschichtungen</a></li>
                    <li><a class="dropdown-item" href="products/dekorative-boden-wandbeschichtungen.html">Dekorative
                        Boden- & Wandbeschichtungen</a></li>
                  </ul>
                </li>
                <li class="nav-item">
                  <a class="nav-link active" href="catalog.html">Katalog</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="/about.html">Über uns</a>
                </li>
              </ul>
              <!-- /.navbar-nav -->

              <div class="offcanvas-footer d-lg-none">
                <div>
                  <a href="mailto:<EMAIL>" class="link-inverse"><EMAIL></a>
                  <br />
                  072 07 04 66 0
                  <br />
                  <nav class="nav social social-white mt-4">
                    <a href="#"><i class="uil uil-twitter"></i></a>
                    <a href="#"><i class="uil uil-facebook-f"></i></a>
                    <a href="#"><i class="uil uil-tiktok"></i></a>
                    <a href="#"><i class="uil uil-instagram"></i></a>
                  </nav>
                  <!-- /.social -->
                </div>
              </div>
              <!-- /.offcanvas-footer -->
            </div>
            <!-- /.offcanvas-body -->
          </div>
          <!-- /.navbar-collapse -->
          <div class="navbar-other w-100 d-flex ms-auto">
            <ul class="navbar-nav flex-row align-items-center ms-auto">
              <li class="nav-item"><a class="nav-link" data-bs-toggle="offcanvas" data-bs-target="#offcanvas-search"><i
                    class="uil uil-search"></i></a></li>
              <li class="nav-item d-none d-md-block">
                <a href="./contact.html" class="btn btn-sm btn-primary rounded-pill">Kontakt</a>
              </li>
              <li class="nav-item d-lg-none">
                <button class="hamburger offcanvas-nav-btn"><span></span></button>
              </li>
            </ul>
            <!-- /.navbar-nav -->
          </div>
          <!-- /.navbar-other -->
        </div>
        <!-- /.container -->
      </nav>
      <!-- /.navbar -->
      <div class="offcanvas offcanvas-top bg-light" id="offcanvas-search" data-bs-scroll="true">
        <div class="container d-flex flex-row py-6">
          <form class="search-form w-100">
            <input id="search-form" type="text" class="form-control" placeholder="Suchbegriff eingeben...">
          </form>
          <!-- /.search-form -->
          <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <!-- /.container -->
      </div>
      <!-- /.offcanvas -->
    </header>
    <!-- /header -->

    <!-- Main Shop Catalog Section -->
    <section class="wrapper bg-light">
      <div class="container pb-14 pb-md-16 pt-12">
        <div class="row gy-10">
          <!-- Hauptinhalt -->
          <div class="col-lg-9 order-lg-2">
            <div class="row align-items-center mb-10 position-relative zindex-1">
              <div class="col-md-7 col-xl-8 pe-xl-20">
                <h2 class="display-6 mb-1">Unser Produkt-Katalog</h2>
                <p class="mb-0 text-muted" id="results-info">6 Ergebnisse</p>
              </div>
              <!--/column -->
              <div class="col-md-5 col-xl-4 ms-md-auto text-md-end mt-5 mt-md-0">
                <div class="form-select-wrapper">
                  <select class="form-select" id="sort-select">
                    <option value="none"><span class="text-muted">Keine Sortierung</span></option>
                    <option value="newest">Neu</option>
                    <option value="name-asc">Alphabetisch: Aufsteigend</option>
                    <option value="name-desc">Alphabetisch: Absteigend</option>
                    <!-- <option value="price-asc">Preis: Aufsteigend</option> -->
                    <!-- <option value="price-desc">Preis: Absteigend</option> -->
                  </select>
                </div>
                <!--/.form-select-wrapper -->
              </div>
              <!--/column -->
            </div>
            <!--/.row -->

            <!-- Grid for Product Items -->
            <div class="grid grid-view projects-masonry shop mb-13">
              <div class="row gx-md-8 gy-10 gy-md-13 isotope">
                <!-- Wiederholte Produkt-Items – Diese Platzhalter werden per Gulp dynamisch ersetzt -->

                <div class="project item col-md-6 col-xl-4" data-index="7" data-name="ISOSEAL© FLEECE" data-price="0.00"
                  data-category="Ergänzungsartikel für Abdichtungsprodukte & Armierungen" data-sizes="120g/m²">
                  <figure class="rounded mb-6">
                    <a href="./shop-products/shop-product-7.html"><img
                        src="https://bright-nest-f97c62ab33.media.strapiapp.com/Chat_GPT_Image_2_Apr_2025_02_50_38_51022bceca.png"
                        alt="ISOSEAL© FLEECE" />
                    </a>

                    <a href="./shop-products/shop-product-7.html" class="item-cart">
                      <i class="uil uil-info"></i> Zum Produkt
                    </a>
                    <span class="avatar bg-aqua text-white w-10 h-10 position-absolute text-uppercase fs-13"
                      style="top: 1rem; left: 1rem;"><span>New!</span></span>
                  </figure>
                  <div class="post-header">
                    <div class="d-flex flex-row align-items-center justify-content-between mb-2">
                      <div class="post-category text-ash mb-0">Ergänzungsartikel für Abdichtungsprodukte & Armierungen
                      </div>
                    </div>
                    <h2 class="post-title h3 fs-22">
                      <a href="./shop-products/shop-product-7.html" class="link-dark">ISOSEAL© FLEECE</a>
                    </h2>
                  </div>
                </div>

                <div class="project item col-md-6 col-xl-4" data-index="8" data-name="ISOSEAL© BOOSTER AD"
                  data-price="0.00" data-category="Abdichtung für Terrassen & Flachdächer" data-sizes="1KG">
                  <figure class="rounded mb-6">
                    <a href="./shop-products/shop-product-8.html"><img
                        src="https://bright-nest-f97c62ab33.media.strapiapp.com/Chat_GPT_Image_2_Apr_2025_02_42_34_f1ab5295fa.png"
                        alt="ISOSEAL© BOOSTER AD" />
                    </a>

                    <a href="./shop-products/shop-product-8.html" class="item-cart">
                      <i class="uil uil-info"></i> Zum Produkt
                    </a>
                    <span class="avatar bg-aqua text-white w-10 h-10 position-absolute text-uppercase fs-13"
                      style="top: 1rem; left: 1rem;"><span>New!</span></span>
                  </figure>
                  <div class="post-header">
                    <div class="d-flex flex-row align-items-center justify-content-between mb-2">
                      <div class="post-category text-ash mb-0">Abdichtung für Terrassen & Flachdächer</div>
                    </div>
                    <h2 class="post-title h3 fs-22">
                      <a href="./shop-products/shop-product-8.html" class="link-dark">ISOSEAL© BOOSTER AD</a>
                    </h2>
                  </div>
                </div>

                <div class="project item col-md-6 col-xl-4" data-index="9" data-name="ISOSEAL© PRIMER PU 1K"
                  data-price="0.00" data-category="Abdichtung für Terrassen & Flachdächer" data-sizes="17KG,5KG,1KG">
                  <figure class="rounded mb-6">
                    <a href="./shop-products/shop-product-9.html"><img
                        src="https://bright-nest-f97c62ab33.media.strapiapp.com/Chat_GPT_Image_2_Apr_2025_02_25_48_d3e869ee19.png"
                        alt="ISOSEAL© PRIMER PU 1K" />
                    </a>

                    <a href="./shop-products/shop-product-9.html" class="item-cart">
                      <i class="uil uil-info"></i> Zum Produkt
                    </a>
                    <span class="avatar bg-aqua text-white w-10 h-10 position-absolute text-uppercase fs-13"
                      style="top: 1rem; left: 1rem;"><span>New!</span></span>
                  </figure>
                  <div class="post-header">
                    <div class="d-flex flex-row align-items-center justify-content-between mb-2">
                      <div class="post-category text-ash mb-0">Abdichtung für Terrassen & Flachdächer</div>
                    </div>
                    <h2 class="post-title h3 fs-22">
                      <a href="./shop-products/shop-product-9.html" class="link-dark">ISOSEAL© PRIMER PU 1K</a>
                    </h2>
                  </div>
                </div>

                <div class="project item col-md-6 col-xl-4" data-index="10" data-name="ISOSEAL© PRIMER PU 2K"
                  data-price="0.00" data-category="Abdichtung für Terrassen & Flachdächer" data-sizes="20KG,4KG,1KG">
                  <figure class="rounded mb-6">
                    <a href="./shop-products/shop-product-10.html"><img
                        src="https://bright-nest-f97c62ab33.media.strapiapp.com/Chat_GPT_Image_2_Apr_2025_02_37_36_4fcce1fba3.png"
                        alt="ISOSEAL© PRIMER PU 2K" />
                    </a>

                    <a href="./shop-products/shop-product-10.html" class="item-cart">
                      <i class="uil uil-info"></i> Zum Produkt
                    </a>
                    <span class="avatar bg-aqua text-white w-10 h-10 position-absolute text-uppercase fs-13"
                      style="top: 1rem; left: 1rem;"><span>New!</span></span>
                  </figure>
                  <div class="post-header">
                    <div class="d-flex flex-row align-items-center justify-content-between mb-2">
                      <div class="post-category text-ash mb-0">Abdichtung für Terrassen & Flachdächer</div>
                    </div>
                    <h2 class="post-title h3 fs-22">
                      <a href="./shop-products/shop-product-10.html" class="link-dark">ISOSEAL© PRIMER PU 2K</a>
                    </h2>
                  </div>
                </div>

                <div class="project item col-md-6 col-xl-4" data-index="11" data-name="ISOSEAL© THIX PU 1K"
                  data-price="0.00" data-category="Abdichtung für Terrassen & Flachdächer" data-sizes="25KG,6KG,1KG">
                  <figure class="rounded mb-6">
                    <a href="./shop-products/shop-product-11.html"><img
                        src="https://bright-nest-f97c62ab33.media.strapiapp.com/Chat_GPT_Image_1_Apr_2025_19_52_32_b9dd9dfb16.png"
                        alt="ISOSEAL© THIX PU 1K" />
                    </a>

                    <a href="./shop-products/shop-product-11.html" class="item-cart">
                      <i class="uil uil-info"></i> Zum Produkt
                    </a>
                    <span class="avatar bg-aqua text-white w-10 h-10 position-absolute text-uppercase fs-13"
                      style="top: 1rem; left: 1rem;"><span>New!</span></span>
                  </figure>
                  <div class="post-header">
                    <div class="d-flex flex-row align-items-center justify-content-between mb-2">
                      <div class="post-category text-ash mb-0">Abdichtung für Terrassen & Flachdächer</div>
                    </div>
                    <h2 class="post-title h3 fs-22">
                      <a href="./shop-products/shop-product-11.html" class="link-dark">ISOSEAL© THIX PU 1K</a>
                    </h2>
                  </div>
                </div>

                <div class="project item col-md-6 col-xl-4" data-index="12" data-name="ISOSEAL© BASIC PU 1K"
                  data-price="0.00" data-category="Abdichtung für Terrassen & Flachdächer"
                  data-sizes="25KG,12KG,6KG,1KG">
                  <figure class="rounded mb-6">
                    <a href="./shop-products/shop-product-12.html"><img
                        src="https://bright-nest-f97c62ab33.media.strapiapp.com/Chat_GPT_Image_1_Apr_2025_19_39_19_7d2f3688dd.png"
                        alt="ISOSEAL© BASIC PU 1K" />
                    </a>

                    <a href="./shop-products/shop-product-12.html" class="item-cart">
                      <i class="uil uil-info"></i> Zum Produkt
                    </a>
                    <span class="avatar bg-aqua text-white w-10 h-10 position-absolute text-uppercase fs-13"
                      style="top: 1rem; left: 1rem;"><span>New!</span></span>
                  </figure>
                  <div class="post-header">
                    <div class="d-flex flex-row align-items-center justify-content-between mb-2">
                      <div class="post-category text-ash mb-0">Abdichtung für Terrassen & Flachdächer</div>
                    </div>
                    <h2 class="post-title h3 fs-22">
                      <a href="./shop-products/shop-product-12.html" class="link-dark">ISOSEAL© BASIC PU 1K</a>
                    </h2>
                  </div>
                </div>

              </div>
              <!-- /.row -->
            </div>
            <!-- /.grid -->

            <!-- Pagination -->
            <nav class="d-flex justify-content-center" aria-label="pagination">
              <ul class="pagination">
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true"><i class="uil uil-arrow-left"></i></span>
                  </a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true"><i class="uil uil-arrow-right"></i></span>
                  </a>
                </li>
              </ul>
              <!-- /.pagination -->
            </nav>
            <!-- /nav -->
          </div>
          <!-- /column -->

          <!-- Sidebar -->
          <aside class="col-lg-3 sidebar">
            <!-- Categories Widget -->
            <div class="widget mt-1">
              <h4 class="widget-title mb-3">Produkt-Kategorien</h4>
              <ul class="list-unstyled ps-0 sidebar-category">

                <li class="mb-1">
                  <a href="#" class="align-items-center rounded link-body" data-bs-toggle="collapse"
                    data-bs-target="#2-collapse" aria-expanded="true">
                    Bauwerksabdichtung <span class="fs-sm text-muted ms-1">(2)</span>
                  </a>
                  <div class="collapse show mt-1" id="2-collapse">
                    <ul class="btn-toggle-nav list-unstyled ps-2">
                      <li><a href="#" class="link-body" data-subcat="Abdichtung für Terrassen & Flachdächer">Abdichtung
                          für Terrassen & Flachdächer</a></li>
                      <li><a href="#" class="link-body"
                          data-subcat="Ergänzungsartikel für Abdichtungsprodukte & Armierungen">Ergänzungsartikel für
                          Abdichtungsprodukte & Armierungen</a></li>
                    </ul>
                  </div>
                </li>

              </ul>
            </div>
            <!-- /.widget -->

            <!-- Size Widget -->
            <div class="widget" id="sidebar-size">
              <h4 class="widget-title mb-3">Size</h4>

              <div class="accordion-item">
                <h4 class="accordion-header">
                  <button class="accordion-button collapsed link-body bg-transparent border-0 fw-semibold" type="button"
                    data-bs-toggle="collapse" data-bs-target="#size-g/m²-collapse" aria-expanded="false">
                    g/m² <span class="fs-sm text-muted ms-1">(1)</span>
                  </button>
                </h4>
                <div id="size-g/m²-collapse" class="accordion-collapse collapse">
                  <div class="accordion-body">

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-g/m²-120" value="120g/m²">
                      <label class="form-check-label" for="size-g/m²-120">
                        120 g/m²
                      </label>
                    </div>

                  </div>
                </div>
              </div>

              <div class="accordion-item">
                <h4 class="accordion-header">
                  <button class="accordion-button collapsed link-body bg-transparent border-0 fw-semibold" type="button"
                    data-bs-toggle="collapse" data-bs-target="#size-KG-collapse" aria-expanded="false">
                    KG <span class="fs-sm text-muted ms-1">(8)</span>
                  </button>
                </h4>
                <div id="size-KG-collapse" class="accordion-collapse collapse">
                  <div class="accordion-body">

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-1" value="1KG">
                      <label class="form-check-label" for="size-KG-1">
                        1 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-4" value="4KG">
                      <label class="form-check-label" for="size-KG-4">
                        4 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-5" value="5KG">
                      <label class="form-check-label" for="size-KG-5">
                        5 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-6" value="6KG">
                      <label class="form-check-label" for="size-KG-6">
                        6 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-12" value="12KG">
                      <label class="form-check-label" for="size-KG-12">
                        12 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-17" value="17KG">
                      <label class="form-check-label" for="size-KG-17">
                        17 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-20" value="20KG">
                      <label class="form-check-label" for="size-KG-20">
                        20 KG
                      </label>
                    </div>

                    <div class="form-check mb-1">
                      <input class="form-check-input" type="checkbox" id="size-KG-25" value="25KG">
                      <label class="form-check-label" for="size-KG-25">
                        25 KG
                      </label>
                    </div>

                  </div>
                </div>
              </div>

            </div>
            <!-- /.widget -->

            <!-- Price Widget -->
            <!-- <div class="widget"> -->
            <!-- <h4 class="widget-title mb-3">Price</h4> -->
            <!-- {{SIDEBAR_PRICE}} -->
            <!-- <div class="row"> -->
            <!-- <div class="col-7 col-md-5 col-lg-12 col-xl-10 col-xxl-10 d-flex align-items-center gap-2">
                  <input class="form-check-input" type="radio" name="price" id="custom-price-range" data-from="0"
                    data-to="999">
                  <label class="form-check-label" for="custom-price-range">
                    <div class="d-flex align-items-center">
                      <input id="custom-price-from" type="number" class="form-control form-control-sm"
                        placeholder="$0.00" min="0" data-from="0">
                      <div class="text-muted mx-2">‒</div>
                      <input id="custom-price-to" type="number" class="form-control form-control-sm"
                        placeholder="$50.00" max="50" data-to="50">
                    </div>
                  </label>
                </div> -->
            <!-- /column -->
            <!-- </div> -->
            <!-- /.row -->
            <!-- </div> -->
            <!-- /.widget -->
          </aside>
          <!-- /column .sidebar -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container -->
    </section>
    <!-- /section -->

    <!-- Extras Section -->
    <section class="wrapper bg-gray">
      <div class="container py-12 py-md-14">
        <div class="row gx-lg-8 gx-xl-12 gy-8">
          <div class="col-md-6 col-lg-4">
            <div class="d-flex flex-row">
              <div>
                <img src="./assets/img/icons/solid/shipment.svg"
                  class="svg-inject icon-svg icon-svg-sm solid-mono text-navy me-4" alt="" />
              </div>
              <div>
                <h4 class="mb-1">Free Shipping</h4>
                <p class="mb-0">Duis mollis gravida commodo id luctus erat porttitor ligula, eget lacinia odio sem.</p>
              </div>
            </div>
          </div>
          <!--/column -->
          <div class="col-md-6 col-lg-4">
            <div class="d-flex flex-row">
              <div>
                <img src="./assets/img/icons/solid/push-cart.svg"
                  class="svg-inject icon-svg icon-svg-sm solid-mono text-navy me-4" alt="" />
              </div>
              <div>
                <h4 class="mb-1">30 Days Return</h4>
                <p class="mb-0">Duis mollis gravida commodo id luctus erat porttitor ligula, eget lacinia odio sem.</p>
              </div>
            </div>
          </div>
          <!--/column -->
          <div class="col-md-6 col-lg-4">
            <div class="d-flex flex-row">
              <div>
                <img src="./assets/img/icons/solid/verify.svg"
                  class="svg-inject icon-svg icon-svg-sm solid-mono text-navy me-4" alt="" />
              </div>
              <div>
                <h4 class="mb-1">2-Years Warranty</h4>
                <p class="mb-0">Duis mollis gravida commodo id luctus erat porttitor ligula, eget lacinia odio sem.</p>
              </div>
            </div>
          </div>
          <!--/column -->
        </div>
        <!--/.row -->
      </div>
      <!-- /.container -->
    </section>
    <!-- /section -->

    <!-- Footer Section -->
    <footer class="bg-dark text-inverse">
      <div class="container py-13 py-md-15">
        <div class="row gy-6 gy-lg-0">
          <div class="col-md-4 col-lg-3">
            <div class="widget">


              <img class="mb-4" height="auto" width="100%" style="transform: translateX(-25%);"
                src="./assets/img/logo.png" srcset="./assets/img/<EMAIL> 2x" alt="ISOPUR Logo" />

              <p class="mb-4">© 2025 Webton e.U. <br class="d-none d-lg-block" />Alle Rechte vorbehalten.</p>

              <!-- <nav class="nav social social-white">
        <a href="#"><i class="uil uil-twitter"></i></a>
        <a href="#"><i class="uil uil-facebook-f"></i></a>
        <a href="#"><i class="uil uil-dribbble"></i></a>
        <a href="#"><i class="uil uil-instagram"></i></a>
        <a href="#"><i class="uil uil-youtube"></i></a>
      </nav> -->
              <!-- /.social -->
            </div>
            <!-- /.widget -->
          </div>
          <!-- /column -->
          <div class="col-md-4 col-lg-4">
            <div class="widget">
              <h4 class="widget-title text-white mb-3">Standort & Kontakt</h4>
              <address class="pe-xl-15 pe-xxl-17"><b>Landstraßer Hauptstraße 146/15/4</b><br />A-1030 Wien, Österreich
              </address>
              <a href="mailto:#"><EMAIL></a><br /> 072 07 04 66 0
            </div>
            <!-- /.widget -->
          </div>
          <!-- /column -->
          <div class="col-md-4 col-lg-2">
            <div class="widget">
              <h4 class="widget-title text-white mb-3">Mehr Links</h4>
              <ul class="list-unstyled  mb-0">
                <li><a class="active" href="./catalog.html">Produkt-Katalog</a></li>
                <li><a href="./about.html">Über uns</a></li>
                <li><a href="./contact.html">Kontakt</a></li>
                <li><a href="./impressum.html">Impressum</a></li>
                <li><a href="#">Datenschutzerklärung</a></li>
              </ul>
            </div>
            <!-- /.widget -->
          </div>
          <!-- /column -->
          <div class="col-md-12 col-lg-3">
            <div style="height: 100%;" class="widget">
              <h6 style="height: 100%;"
                class="widget-title d-flex justify-content-center align-items-center text-white mb-3">
                Made with&nbsp;<span style="color: red;">&#10084;</span>&nbsp;by Webton!</h6>
              <!-- <h4 class="widget-title text-white mb-3">Our Newsletter</h4> -->
              <!-- <p class="mb-5">Subscribe to our newsletter to get our news & deals delivered to you.</p> -->
              <!-- <div class="newsletter-wrapper"> -->
              <!-- Begin Mailchimp Signup Form -->
              <!-- <div id="mc_embed_signup2"> -->
              <!-- <form action="https://elemisfreebies.us20.list-manage.com/subscribe/post?u=aa4947f70a475ce162057838d&amp;id=b49ef47a9a" method="post" id="mc-embedded-subscribe-form2" name="mc-embedded-subscribe-form" class="validate dark-fields" target="_blank" novalidate> -->
              <!-- <div id="mc_embed_signup_scroll2"> -->
              <!-- <div class="mc-field-group input-group form-floating"> -->
              <!-- <input type="email" value="" name="EMAIL" class="required email form-control" placeholder="Email Address" id="mce-EMAIL2"> -->
              <!-- <label for="mce-EMAIL2">Email Address</label> -->
              <!-- <input type="submit" value="Join" name="subscribe" id="mc-embedded-subscribe2" class="btn btn-primary "> -->
              <!-- </div> -->
              <!-- <div id="mce-responses2" class="clear"> -->
              <!-- <div class="response" id="mce-error-response2" style="display:none"></div> -->
              <!-- <div class="response" id="mce-success-response2" style="display:none"></div> -->
              <!-- </div> real people should not fill this in and expect good things - do not remove this or risk form bot signups -->
              <!-- <div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text" name="b_ddc180777a163e0f9f66ee014_4b1bcfa0bc" tabindex="-1" value=""></div> -->
              <!-- <div class="clear"></div> -->
              <!-- </div> -->
              <!-- </form> -->
              <!-- </div> -->
              <!--End mc_embed_signup-->
              <!-- </div> -->
              <!-- /.newsletter-wrapper -->
            </div>
            <!-- /.widget -->
          </div>
          <!-- /column -->
        </div>
        <!--/.row -->

      </div>
      <!-- /.container -->
    </footer>

    <div class="progress-wrap">
      <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
      </svg>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"
      type="application/javascript"></script>
    <script src="./assets/js/plugins.js" type="application/javascript"></script>
    <script src="./assets/js/theme.js" type="application/javascript"></script>
    <script src="./assets/js/pdf-viewer.js" type="application/javascript"></script>



    <!-- Custom Filtering Logic for Catalog-Products -->
    <style>
      /* Style for hidden items in Isotope grid */
      .isotope .hidden {
        display: none !important;
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        const searchInput = document.getElementById('search-form');
        // Hier greifen wir auf den Container der Produkt-Items zu:
        const gridContainer = document.querySelector('.isotope');
        const wishlistIcons = document.querySelectorAll('.item-like');

        // Initialize Isotope once at page load
        let iso = new Isotope(gridContainer, {
          itemSelector: '.project.item',
          layoutMode: 'masonry',
          percentPosition: true,
          transitionDuration: '0.4s',
          stagger: 30,
          getSortData: {
            name: '[data-name]',
            // price: function(itemElem) {
            //   return parseFloat(itemElem.getAttribute('data-price'));
            // },
            index: function (itemElem) {
              return parseFloat(itemElem.getAttribute('data-index'));
            }
          }
        });

        // Make sure images are loaded before initializing layout
        imagesLoaded(gridContainer).on('progress', function () {
          iso.layout();
        });

        // wishlistIcons.forEach((heartIcon) => {
        //   heartIcon.addEventListener('click', function (e) {
        //     // Prüfe, ob das geklickte Element (oder eines seiner Eltern) das Heart-Icon ist
        //     e.preventDefault();
        //     // Bestimme das zugehörige Produkt-Item
        //     const productItem = heartIcon.closest('.project.item');
        //     if (!productItem) return;

        //     // Lese die relevanten Daten aus den Data-Attributen
        //     const id = productItem.getAttribute('data-index');  // Hier als Produkt-ID
        //     const name = productItem.getAttribute('data-name');
        //     const priceStr = productItem.getAttribute('data-price');
        //     const price = parseFloat(priceStr);
        //     // Hole das Bild aus dem <img>-Tag
        //     const imgElement = productItem.querySelector('img');
        //     const image = imgElement ? imgElement.getAttribute('src') : '';

        //     // Erstelle ein Objekt mit den Produktdaten
        //     const wishlistItem = {
        //       id: id,
        //       name: name,
        //       price: price,
        //       image: image,
        //       quantity: 1  // Standardmäßig 1, kann später angepasst werden
        //     };

        //     // Füge das Produkt über unsere globale Funktion zur Wunschliste hinzu
        //     window.addToWishlist(wishlistItem);

        //     // Optional: Visuelles Feedback geben (z. B. Icon färben, Toast anzeigen etc.)
        //     heartIcon.classList.add('color-red');
        //   });
        // });

        // Alle Produkt-Items (zum initialen Laden – hier wird angenommen, dass jedes Element ein data-index besitzt)
        let productItems = Array.from(document.querySelectorAll('.project.item'));

        // Ensure all items have necessary data attributes for filtering and sorting
        productItems.forEach((item, index) => {
          // If data-index is missing, set it based on DOM order
          if (!item.hasAttribute('data-index')) {
            item.setAttribute('data-index', index);
          }
          // Ensure other attributes exist with defaults if needed
          if (!item.hasAttribute('data-price')) {
            item.setAttribute('data-price', '0.00');
          }
          if (!item.hasAttribute('data-sizes')) {
            item.setAttribute('data-sizes', '');
          }
        });

        // Globale Filterkriterien
        let searchString = '';
        let activeCategory = null;
        let activeSizes = new Set();
        let activePriceRange = null;

        // Sortierkriterium (Standard: none)
        let activeSort = 'none';

        // --- Search Filtering ---
        searchInput.addEventListener('input', () => {
          searchString = searchInput.value.trim().toLowerCase();
          filterAndSortProducts();
        });

        // --- Category Filtering ---
        const categoryLinks = document.querySelectorAll('.sidebar-category a');
        categoryLinks.forEach(link => {
          link.addEventListener('click', (e) => {
            e.preventDefault();
            categoryLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            activeCategory = link.getAttribute('data-subcat');
            filterAndSortProducts();
          });
        });

        // --- Size Filtering ---
        const sizeCheckboxes = document.querySelectorAll('#sidebar-size input[type="checkbox"]');
        sizeCheckboxes.forEach(checkbox => {
          checkbox.addEventListener('change', () => {
            activeSizes = new Set(
              Array.from(sizeCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value.trim())
            );
            filterAndSortProducts();
          });
        });

        // --- Price Filtering ---
        // const priceRadios = document.querySelectorAll('input[name="price"]');
        // priceRadios.forEach(radio => {
        //   radio.addEventListener('change', () => {
        //     const from = parseFloat(radio.getAttribute('data-from'));
        //     const to = parseFloat(radio.getAttribute('data-to'));
        //     activePriceRange = { from, to };
        //     filterAndSortProducts();
        //   });
        // });

        // const customPriceFrom = document.getElementById('custom-price-from');
        // const customPriceTo = document.getElementById('custom-price-to');
        // const customPriceRadio = document.getElementById('custom-price-range');

        // customPriceFrom.addEventListener('change', () => {
        //   const from = parseFloat(customPriceFrom.value);
        //   customPriceFrom.setAttribute('data-from', from);
        //   if (customPriceRadio.checked) {
        //     const to = parseFloat(customPriceTo.value) || parseFloat(customPriceTo.getAttribute('data-to'));
        //     activePriceRange = { from: (from || 0), to };
        //     filterAndSortProducts();
        //   }
        // });

        // customPriceTo.addEventListener('change', () => {
        //   const to = parseFloat(customPriceTo.value);
        //   customPriceTo.setAttribute('data-to', to);
        //   if (customPriceRadio.checked) {
        //     const from = parseFloat(customPriceFrom.value) || parseFloat(customPriceFrom.getAttribute('data-from'));
        //     activePriceRange = { from, to };
        //     filterAndSortProducts();
        //   }
        // });

        // --- Sortierung ---
        const sortSelect = document.getElementById('sort-select');
        sortSelect.addEventListener('change', (e) => {
          activeSort = e.target.value;
          filterAndSortProducts();
        });

        // --- Filter- und Sortierfunktion ---
        function filterAndSortProducts() {
          const resultsInfo = document.getElementById('results-info');

          // Apply filters to all items first
          productItems.forEach(item => {
            let show = true;
            const price = parseFloat(item.getAttribute('data-price'));
            const category = item.getAttribute('data-category');
            const sizesStr = item.getAttribute('data-sizes') || "";
            const sizes = sizesStr.split(',').map(s => s.trim());

            if (searchString) {
              const name = item.getAttribute('data-name').toLowerCase();
              show = name.includes(searchString);
            }

            if (activeCategory && category !== activeCategory) {
              show = false;
            }

            if (activeSizes.size > 0) {
              const hasSize = Array.from(activeSizes).some(size => sizes.includes(size));
              if (!hasSize) show = false;
            }

            if (activePriceRange) {
              if (price < activePriceRange.from || price > activePriceRange.to) {
                show = false;
              }
            }

            // Set the display property for Isotope filtering
            if (show) {
              item.classList.remove('hidden');
            } else {
              item.classList.add('hidden');
            }
          });

          // Get visible items for counting and sorting
          let visibleItems = productItems.filter(item => !item.classList.contains('hidden'));

          // Update results info
          resultsInfo.innerHTML =
            (visibleItems.length < 1 ? "Keine" : visibleItems.length) +
            (visibleItems.length === 1 ? " Ergebnis" : " Ergebnisse");

          // Prepare sorting options for Isotope
          let sortBy = 'original-order';
          let sortAscending = true;

          // Set sorting parameters based on active sort
          if (activeSort !== 'none') {
            switch (activeSort) {
              case 'name-asc':
                sortBy = 'name';
                sortAscending = true;
                break;
              case 'name-desc':
                sortBy = 'name';
                sortAscending = false;
                break;
              case 'price-asc':
                sortBy = 'price';
                sortAscending = true;
                break;
              case 'price-desc':
                sortBy = 'price';
                sortAscending = false;
                break;
              case 'newest':
                sortBy = 'index';
                sortAscending = false;
                break;
            }
          }

          // Update Isotope layout with filtering and sorting
          iso.arrange({
            filter: function (itemElem) {
              return !itemElem.classList.contains('hidden');
            },
            sortBy: sortBy,
            sortAscending: sortAscending
          });
        }

        // Wir nutzen jetzt die integrierte Isotope-Sortierfunktionalität
        // Die sortProducts-Funktion wird nicht mehr benötigt, da Isotope die Sortierung übernimmt

        // Initialer Aufruf, falls du sofort filtern oder sortieren möchtest
        filterAndSortProducts();
      });
    </script>
  </div>
  <!-- /.content-wrapper -->
</body>

</html>