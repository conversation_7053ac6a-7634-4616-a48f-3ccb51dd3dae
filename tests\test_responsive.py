import os
from pathlib import Path
import pytest
from playwright.sync_api import Page, expect

BASE_PATH = Path(__file__).resolve().parents[1]

@pytest.mark.parametrize("viewport", [(375, 667), (768, 1024), (1280, 800)])
def test_homepage_layout(page: Page, viewport):
    """Check homepage loads without layout issues at various viewports."""
    page.set_viewport_size({"width": viewport[0], "height": viewport[1]})
    page.goto(BASE_PATH.joinpath("index.html").as_uri())
    expect(page.locator(".modern-card")).to_have_count(6)


def test_search_category_count(page: Page):
    """Ensure search page lists all 6 categories."""
    page.set_viewport_size({"width": 768, "height": 800})
    page.goto(BASE_PATH.joinpath("products", "search.html").as_uri())
    expect(page.locator("#categories .modern-card")).to_have_count(6)


def test_nonexistent_page(page: Page):
    """Expect error for missing page."""
    with pytest.raises(Exception):
        page.goto(BASE_PATH.joinpath("nosuchpage.html").as_uri())
