/**
 * PDF Viewer (Bootstrap-Modal + PDF.js)
 * -------------------------------------
 * - <PERSON><PERSON><PERSON>, sobald <PERSON> sichtbar ist
 * - ResizeObserver statt setTimeout-Debounce
 * - <PERSON><PERSON><PERSON>, Zoom-Buttons vorbereitet
 * - Touch-, Keyboard- und Fehler-Handling
 */

document.addEventListener("DOMContentLoaded", () => {
  /* ─────────────────────────────  State  ───────────────────────────── */
  let pdf = null; // aktuelles PDF.js-Dokument
  let pdfUrl = null; // aktuell geladene URL
  let pageNum = 1; // aktuelle Seite
  let zoom = 1; // aktuelles Zoom-Level
  let firstRenderDue = false; // PDF geladen, erste Seite aber noch nicht gerendert
  const fileNameCategoryMap = {
    "Produktkatalog_ISOMAT.pdf": "Vollständiger Produktkatalog",
    "Bauwerksabdichtung_Seiten-aus-Produktkatalog-ISOMAT-1.pdf":
      "Bauwerksabdichtung",
    "WDV-Systeme_Seiten-aus-Produktkatalog-ISOMAT-2.pdf": "WDV-Systeme",
    "Farben_Oberflaechenschutz_Seiten-aus-Produktkatalog-ISOMAT-3.pdf":
      "Farben & Oberflächenschutz",
    "Fliesen_Natursteinverlegung_Seiten-aus-Produktkatalog-ISOMAT-4.pdf":
      "Fliesen- & Natursteinverlegung",
    "Betonherstellung_instandsetzung_Seiten-aus-Produktkatalog-ISOMAT-5.pdf":
      "Betonherstellung & -Instandsetzung",
    "Mauerwerkherstellung_Instandsetzung_Seiten-aus-Produktkatalog-ISOMAT-6.pdf":
      "Mauerwerkherstellung & -Instandsetzung",
    "Industriebeschichtungen_Seiten-aus-Produktkatalog-ISOMAT-7.pdf":
      "Industriebeschichtungen",
    "Dekorative_Boden_Wandbeschichtungen_Seiten-aus-Produktkatalog-ISOMAT-8.pdf":
      "Dekorative Boden- & Wandbeschichtungen",
  };

  /* ─────────────────────────────  DOM  ───────────────────────────── */
  const pdfViewerModal = document.getElementById("pdfViewerModal");
  const pdfViewer = document.getElementById("pdfViewer");
  const pdfDownloadLink = document.getElementById("pdfDownloadLink");
  // const pdfViewerModalLabel = document.getElementById("pdfViewerModalLabel");
  const pdfViewerFileName = document.getElementById("pdfViewerFileName");
  const pdfLoadingSpinner = document.getElementById("pdfLoadingSpinner");
  const pdfError = document.getElementById("pdfError");
  const pdfPageInfo = document.getElementById("pdfPageInfo");
  const pdfPrevPage = document.getElementById("pdfPrevPage");
  const pdfNextPage = document.getElementById("pdfNextPage");
  const pdfZoomOut = document.getElementById("pdfZoomOut");
  const pdfZoomIn = document.getElementById("pdfZoomIn");
  const pdfZoomFit = document.getElementById("pdfZoomFit");
  const pdfZoomSlider = document.getElementById("pdfZoomSlider");
  const categoryLinks = document.querySelectorAll(".category-pdf-link");


  /* ─────────────────────────────  Initialize PDF.js  ───────────────────────────── */
  pdfjsLib.GlobalWorkerOptions.workerSrc =
    "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";

  // Pre-warm the PDF.js worker
  pdfjsLib
    .getDocument({
      url: "./assets/data/Produktkatalog_ISOMAT.pdf",
      cMapUrl: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/",
      cMapPacked: true,
    })
    .promise.catch(() => {
      // Ignore error, this is just to initialize the worker
      console.log("PDF.js worker initialized");
    });

  /* ─────────────────────────────  Helper  ───────────────────────────── */
  const localPdfBaseUrl = "./assets/data/"; // ggf. dynamisch anpassen

  const isMobileDevice = () => {
    return (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      ) || window.innerWidth <= 768
    );
  };

  const getOptimalZoom = () => {
    return isMobileDevice() ? 1 : 0.8; // Basis-Zoom für die initiale Anzeige
  };

  const setLoading = (on) => {
    pdfLoadingSpinner.classList.toggle("d-none", !on);
    pdfViewer.style.opacity = on ? "0.3" : "1";
  };

  const showError = (err) => {
    if (err) console.error(err);
    setLoading(false);
    pdfError.classList.remove("d-none");
  };

  const hideError = () => pdfError.classList.add("d-none");

  const getViewport = (page) => {
    const baseViewport = page.getViewport({ scale: 1 });
    const containerWidth = pdfViewer.clientWidth;
    const containerHeight = pdfViewer.clientHeight;
    const containerAspectRatio = containerWidth / containerHeight;
    const pageAspectRatio = baseViewport.width / baseViewport.height;

    let scale;
    if (isMobileDevice()) {
      // Für mobile Geräte: Optimierte Breitenanpassung
      scale = (containerWidth / baseViewport.width) * zoom;
    } else {
      // Für Desktop: Beste Anpassung an Container
      scale =
        Math.min(
          containerWidth / baseViewport.width,
          containerHeight / baseViewport.height
        ) * zoom;
    }

    return page.getViewport({ scale });
  };

  let currentZoomAnimation = null;
  const smoothZoom = (targetZoom, duration = 200) => {
    if (currentZoomAnimation) {
      cancelAnimationFrame(currentZoomAnimation);
    }

    const startZoom = zoom;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing-Funktion für sanftere Animation
      const easeProgress =
        progress < 0.5
          ? 2 * progress * progress
          : -1 + (4 - 2 * progress) * progress;

      zoom = startZoom + (targetZoom - startZoom) * easeProgress;

      renderPage(pageNum);

      if (progress < 1) {
        currentZoomAnimation = requestAnimationFrame(animate);
      } else {
        currentZoomAnimation = null;
      }
    };

    animate();
  };

  const handleViewerGestures = (() => {
    let touchStartX = 0;
    let touchStartY = 0;
    let lastDistance = 0;
    let initialPinchDistance = 0;
    let isZooming = false;
    let lastZoomTime = 0;

    return {
      start: (e) => {
        if (e.touches.length === 2) {
          isZooming = true;
          initialPinchDistance = Math.hypot(
            e.touches[0].clientX - e.touches[1].clientX,
            e.touches[0].clientY - e.touches[1].clientY
          );
          lastDistance = initialPinchDistance;
        } else {
          touchStartX = e.touches[0].clientX;
          touchStartY = e.touches[0].clientY;
          isZooming = false;
        }
      },

      move: (e) => {
        if (e.touches.length === 2 && isZooming) {
          const currentDistance = Math.hypot(
            e.touches[0].clientX - e.touches[1].clientX,
            e.touches[0].clientY - e.touches[1].clientY
          );

          if (lastDistance > 0) {
            const deltaDistance = currentDistance - lastDistance;
            const now = Date.now();

            // Zeitbasierte Zoom-Geschwindigkeit
            if (now - lastZoomTime > 32) {
              // ~30fps throttling
              const zoomDelta = deltaDistance * 0.005; // Sanftere Zoom-Änderung
              const newZoom = Math.max(
                0.5,
                Math.min(3, zoom * (1 + zoomDelta))
              );

              if (Math.abs(newZoom - zoom) > 0.01) {
                zoom = newZoom;
                renderPage(pageNum);
                lastZoomTime = now;
              }
            }
          }
          lastDistance = currentDistance;
          e.preventDefault();
        }
      },

      end: (e) => {
        if (!pdf || firstRenderDue) return;

        if (!isZooming && e.touches.length === 0) {
          const touchEndX = e.changedTouches[0].clientX;
          const touchEndY = e.changedTouches[0].clientY;

          const dx = touchEndX - touchStartX;
          const dy = touchEndY - touchStartY;

          // Horizontale Wischgeste für Seitenwechsel
          if (Math.abs(dx) > 50 && Math.abs(dy) < Math.abs(dx) * 0.5) {
            if (dx > 0 && pageNum > 1) {
              handlePageChange(-1);
            } else if (dx < 0 && pageNum < pdf.numPages) {
              handlePageChange(1);
            }
          }
        }

        isZooming = false;
        lastDistance = 0;
        initialPinchDistance = 0;
      },
    };
  })();

  /* ─────────────────────────────  Render  ───────────────────────────── */
  const renderPage = async (num) => {
    try {
      setLoading(true);

      // Render-Feedback hinzufügen
      const progressIndicator = document.createElement("div");
      progressIndicator.className = "render-progress";
      pdfViewer.appendChild(progressIndicator);

      const page = await pdf.getPage(num);
      const viewport = getViewport(page);
      const canvas = document.createElement("canvas");
      const pixelRatio = window.devicePixelRatio || 1;

      // Höhere Auflösung für schärfere Darstellung
      canvas.width = viewport.width * pixelRatio;
      canvas.height = viewport.height * pixelRatio;
      canvas.style.width = `${viewport.width}px`;
      canvas.style.height = `${viewport.height}px`;

      const ctx = canvas.getContext("2d");
      ctx.scale(pixelRatio, pixelRatio);

      const renderTask = page.render({
        canvasContext: ctx,
        viewport,
        enableWebGL: true,
      });

      renderTask.promise
        .then(() => {
          const wrapper = document.createElement("div");
          wrapper.className = "pdf-page-wrapper";
          wrapper.style.opacity = "0";
          wrapper.style.transition = "opacity .25s ease";
          wrapper.appendChild(canvas);

          pdfViewer.replaceChildren(wrapper);
          requestAnimationFrame(() => {
            wrapper.style.opacity = "1";
            progressIndicator.remove();
          });

          pageNum = num;
          pdfPageInfo.textContent = `Seite ${pageNum} von ${pdf.numPages}`;
          pdfPrevPage.disabled = pageNum <= 1;
          pdfNextPage.disabled = pageNum >= pdf.numPages;

          // Cache nächste und vorherige Seite für schnelleres Blättern
          if (pageNum < pdf.numPages) pdf.getPage(pageNum + 1);
          if (pageNum > 1) pdf.getPage(pageNum - 1);

          setLoading(false);
        })
        .catch(showError);
    } catch (e) {
      showError(e);
    }
  };

  /* ─────────────────────────────  Load  ───────────────────────────── */
  const loadPdf = async (url) => {
    const task = pdfjsLib.getDocument({
      url,
      cMapUrl: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/",
      cMapPacked: true,
    });

    try {
      setLoading(true);
      hideError();
      pdfViewer.innerHTML = "";

      task.onProgress = async (p) => {
        const pct = ((p.loaded / (p.total || 1)) * 100).toFixed(0);
        console.info(`PDF loading: ${pct}%`);
      };

      pdf = await task.promise;
      pageNum = 1;
      zoom = getOptimalZoom();
      firstRenderDue = true;

      await renderPage(1);
      pdfPageInfo.textContent = `Seite 1 von ${pdf.numPages}`;
      pdfPrevPage.disabled = true;
      pdfNextPage.disabled = pdf.numPages <= 1;
    } catch (e) {
      showError(e);
    }
  };

  /* ─────────────────────────────  Modal  ───────────────────────────── */
  const modal = new bootstrap.Modal(pdfViewerModal);

  pdfViewerModal.addEventListener("shown.bs.modal", () => {
    if (firstRenderDue) {
      firstRenderDue = false;
      renderPage(1).catch(showError);
    }

    // Aktiviere Bildschirm-Wake-Lock für bessere Leseerfahrung
    try {
      if ("wakeLock" in navigator) {
        navigator.wakeLock.request("screen").catch(console.warn);
      }
    } catch (err) {
      console.warn("Wake Lock API nicht verfügbar:", err);
    }

    /* ─────────────────────────────  Event Listeners  ───────────────────────────── */
    const handleZoom = (newZoom) => {
      // Begrenzen Sie den Zoom auf sinnvolle Werte
      newZoom = Math.max(0.5, Math.min(2.5, newZoom));
      zoom = newZoom;

      // Aktualisieren Sie den Slider basierend auf dem relativen Zoom
      // 100% entspricht dem optimalen Zoom-Level
      const baseZoom = getOptimalZoom();
      const relativeZoom = (newZoom / baseZoom) * 100;
      pdfZoomSlider.value = Math.round(relativeZoom);

      renderPage(pageNum);
    };

    const handleZoomFit = async () => {
      try {
        const page = await pdf.getPage(pageNum);
        const viewport = page.getViewport({ scale: 1 });
        const containerWidth = pdfViewer.clientWidth - 32; // Berücksichtige Padding
        const scale = containerWidth / viewport.width;
        handleZoom(scale);
      } catch (e) {
        console.warn("Fehler beim Anpassen der Seite:", e);
      }
    };

    // Zoom Controls
    pdfZoomOut.addEventListener("click", () => handleZoom(zoom * 0.9));
    pdfZoomIn.addEventListener("click", () => handleZoom(zoom * 1.1));
    pdfZoomFit.addEventListener("click", handleZoomFit);

    // Zoom Slider
    pdfZoomSlider.addEventListener("input", (e) => {
      const baseZoom = getOptimalZoom();
      const relativeZoom = parseFloat(e.target.value) / 100;
      handleZoom(baseZoom * relativeZoom);
    });

    // Simple touch handling for page navigation only
    let touchStartX = 0;
    pdfViewer.addEventListener(
      "touchstart",
      (e) => {
        touchStartX = e.touches[0].clientX;
      },
      { passive: true }
    );

    pdfViewer.addEventListener(
      "touchend",
      (e) => {
        if (!pdf || firstRenderDue) return;

        const touchEndX = e.changedTouches[0].clientX;
        const dx = touchEndX - touchStartX;

        // Nur horizontale Wischgesten mit signifikanter Bewegung
        if (Math.abs(dx) > 50) {
          if (dx > 0 && pageNum > 1) {
            handlePageChange(-1);
          } else if (dx < 0 && pageNum < pdf.numPages) {
            handlePageChange(1);
          }
        }
      },
      { passive: true }
    );
  });

  pdfViewerModal.addEventListener("hidden.bs.modal", () => {
    pdfViewer.innerHTML = "";
    pdf = null;
    zoom = getOptimalZoom();
    firstRenderDue = false;
    setLoading(false);
    hideError();
  });

  /* ─────────────────────────────  Navigation  ───────────────────────────── */
  // Verbesserte Navigation mit visuellen Feedback
  const handlePageChange = (direction) => {
    const newPage = pageNum + direction;
    if (newPage >= 1 && newPage <= pdf.numPages) {
      // Visuelles Feedback für die Richtung
      const current = pdfViewer.querySelector(".pdf-page-wrapper");
      if (current) {
        current.style.transform = `translateX(${-direction * 20}px)`;
        current.style.opacity = "0.5";
        setTimeout(() => renderPage(newPage), 50);
      } else {
        renderPage(newPage);
      }
    }
  };

  pdfPrevPage.addEventListener("click", () => handlePageChange(-1));
  pdfNextPage.addEventListener("click", () => handlePageChange(1));

  // Tastatur-Navigation mit visueller Rückmeldung
  document.addEventListener("keydown", (e) => {
    if (!pdf || firstRenderDue) return;

    let handled = true;

    switch (e.key) {
      case "ArrowLeft":
        if (pageNum > 1) handlePageChange(-1);
        break;
      case "ArrowRight":
        if (pageNum < pdf.numPages) handlePageChange(1);
        break;
      case "+":
      case "=":
        if (zoom < 3) {
          zoom = Math.min(zoom * 1.2, 3);
          renderPage(pageNum);
        }
        break;
      case "-":
        if (zoom > 0.5) {
          zoom = Math.max(zoom * 0.8, 0.5);
          renderPage(pageNum);
        }
        break;
      default:
        handled = false;
    }

    if (handled) {
      e.preventDefault();
    }
  });

  /* ─────────────────────────────  ResizeObserver  ───────────────────────────── */
  new ResizeObserver(() => {
    if (pdf && !firstRenderDue) renderPage(pageNum);
  }).observe(pdfViewer);

  /* ─────────────────────────────  Retry Button  ───────────────────────────── */
  window.retryLoadPdf = async () => {
    if (pdfUrl) {
      hideError();
      await loadPdf(pdfUrl);
    }
  };

  /* ─────────────────────────────  Link-Handler  ───────────────────────────── */
  categoryLinks.forEach((link) => {
    link.addEventListener("click", async (e) => {
      e.preventDefault();

      const fileName = link.dataset.pdf;
      pdfUrl = `${localPdfBaseUrl}${fileName}`;

      if (fileNameCategoryMap[fileName] !== "") {
        console.log(pdfViewerFileName)
        pdfViewerFileName.innerHTML = `<b>Kategorie:</b> ${fileNameCategoryMap[fileName]}`;
        pdfViewerFileName.classList = ["modal-subheader text-primary"];
      }

      pdfDownloadLink.href = pdfUrl;
      pdfDownloadLink.download = fileName;

      modal.show();
      await loadPdf(pdfUrl);
    });
  });

  /* ─────────────────────────────  Bootstrap-Tooltips  ───────────────────────────── */
  [...document.querySelectorAll('[data-bs-toggle="tooltip"]')].forEach(
    (el) => new bootstrap.Tooltip(el)
  );

  /* ─────────────────────────────  Styles  ───────────────────────────── */
  const style = document.createElement("style");
  style.textContent = `
    .pdf-page-wrapper {
      transition: transform 0.3s ease, opacity 0.3s ease;
      -webkit-tap-highlight-color: transparent;
      touch-action: pan-x pan-y pinch-zoom; /* Erlaube Scrollen und Pinch-Zoom */
      user-select: none;
      will-change: transform, opacity;
    }
    
    .render-progress {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(90deg, #007bff 0%, #28a745 100%);
      animation: progress 1s infinite linear;
      z-index: 1000;
    }
    
    @keyframes progress {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    
    #pdfViewer {
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
      padding: 1rem;
      min-height: 50vh;
    }
    
    #pdfViewer canvas {
      max-width: 100%;
      height: auto !important;
      display: block;
      margin: 0 auto;
      backface-visibility: hidden;
    }
    
    .btn-outline-primary:active {
      transform: scale(0.95);
      transition: transform 0.1s;
    }
    
    /* Zoom Controls Styling */
    .form-range {
      height: 1.5rem;
      padding: 0;
      background: transparent;
    }
    
    .form-range::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background: #0d6efd;
      cursor: pointer;
    }
    
    .form-range::-webkit-slider-runnable-track {
      width: 100%;
      height: 4px;
      background: #dee2e6;
      border-radius: 2px;
    }
    
    @media (max-width: 768px) {
      #pdfViewer {
        padding: 0.5rem;
      }
      
      .pdf-page-wrapper {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateZ(0);
      }
      
      #pdfViewerModal .modal-content {
        border-radius: 0;
      }
      
      #pdfViewer canvas {
        touch-action: pan-x pan-y; /* Nur horizontales und vertikales Scrollen erlauben */
      }
      
      .modal-footer .row {
        flex-direction: column;
      }
      
      .modal-footer .col-12 {
        width: 100%;
        margin-bottom: 0.5rem;
      }
      
      .form-range {
        width: 120px !important;
      }
    }
  `;
  document.head.appendChild(style);
});
