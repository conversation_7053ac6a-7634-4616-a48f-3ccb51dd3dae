/* Responsive fixes for IsoPur website */
.icon-wrapper {
    overflow: hidden;
}

.card-title,
.card-subtitle,
.modern-list li,
footer address,
footer .widget {
    overflow-wrap: anywhere;
}

/* Ensure cards stack with consistent spacing */
@media (max-width: 575.98px) {
    .modern-card {
        margin-bottom: 1rem;
    }
}

/* Subcategory grid alignment */
.subcategory-item {
    min-height: 100%;
}
@media (max-width: 575.98px) {
    .subcategory-item {
        margin-bottom: 1rem;
    }
}

/* Footer spacing on tablets */
@media (max-width: 992px) {
    footer .row.gy-lg-0 {
        gap: 1.5rem;
    }
}
