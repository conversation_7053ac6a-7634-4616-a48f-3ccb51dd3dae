#!/usr/bin/env python3
"""
Automatische Korrektur der fehlerhaften Links in der IsoPur Website
"""

import os
import re
import glob
from pathlib import Path
import shutil

class LinkFixer:
    def __init__(self, root_dir="."):
        self.root_dir = Path(root_dir)
        self.fixes_applied = []
        self.issues_found = []
        
    def fix_shop_product_links(self):
        """Korrigiert die relativen Pfade in den Shop-Produkt-Dateien"""
        shop_files = list(self.root_dir.glob("shop-products/*.html"))
        
        for shop_file in shop_files:
            print(f"Korrigiere Links in: {shop_file.name}")
            
            with open(shop_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Korrigiere alle ./assets/ zu ../assets/ in Shop-Produkten
            content = re.sub(r'(href|src)="\.\/assets\/', r'\1="../assets/', content)

            # Korrigiere auch srcset Attribute
            content = re.sub(r'srcset="\.\/assets\/', r'srcset="../assets/', content)

            # Korrigiere /#products zu #products in Shop-Produkten
            content = re.sub(r'href="\/\#products"', r'href="#products"', content)
            
            # Korrigiere alle ./index.html zu ../index.html
            content = re.sub(r'(href)="\.\/index\.html"', r'\1="../index.html"', content)
            
            # Korrigiere alle ./contact.html zu ../contact.html
            content = re.sub(r'(href)="\.\/contact\.html"', r'\1="../contact.html"', content)
            
            # Korrigiere alle ./catalog.html zu ../catalog.html
            content = re.sub(r'(href)="\.\/catalog\.html"', r'\1="../catalog.html"', content)
            
            # Korrigiere alle ./about.html zu ../about.html
            content = re.sub(r'(href)="\.\/about\.html"', r'\1="../about.html"', content)
            
            # Korrigiere alle ./impressum.html zu ../impressum.html
            content = re.sub(r'(href)="\.\/impressum\.html"', r'\1="../impressum.html"', content)
            
            # Korrigiere alle ./terms.html zu ../terms.html
            content = re.sub(r'(href)="\.\/terms\.html"', r'\1="../terms.html"', content)
            
            if content != original_content:
                with open(shop_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.fixes_applied.append(f"Shop-Produkt {shop_file.name}: Relative Pfade korrigiert")
                
    def fix_missing_logo_file(self):
        """Erstellt <NAME_EMAIL> Datei"""
        source_logo = self.root_dir / "assets/img/logo.png"
        target_logo = self.root_dir / "assets/img/<EMAIL>"
        
        if source_logo.exists() and not target_logo.exists():
            shutil.copy2(source_logo, target_logo)
            self.fixes_applied.append("Fehlende <EMAIL> erstellt (Kopie von logo.png)")
        elif not source_logo.exists():
            self.issues_found.append("Weder logo.<NAME_EMAIL> gefunden")
            
    def fix_hash_links(self):
        """Korrigiert /#products Links zu #products"""
        html_files = list(self.root_dir.glob("*.html"))
        
        for html_file in html_files:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Korrigiere /#products zu #products
            content = re.sub(r'href="\/\#products"', r'href="#products"', content)
            
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.fixes_applied.append(f"{html_file.name}: /#products zu #products korrigiert")
                
    def fix_tel_links(self):
        """Korrigiert tel: Links - diese sind eigentlich korrekt, aber werden als fehlerhaft erkannt"""
        # tel: Links sind eigentlich korrekt, wir müssen nur den Link-Checker anpassen
        # Für jetzt dokumentieren wir das als bekanntes Problem
        self.issues_found.append("tel: Links sind korrekt, aber werden vom Checker als fehlerhaft erkannt")
        
    def create_missing_shop_page(self):
        """Erstellt eine einfache shop.html Seite oder entfernt den Link"""
        catalog_file = self.root_dir / "catalog.html"
        
        if catalog_file.exists():
            with open(catalog_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Entferne oder korrigiere den Link zu shop.html?page=1
            # Da es keine shop.html gibt, entfernen wir den Link oder ersetzen ihn
            original_content = content
            content = re.sub(r'href="\.\/shop\.html\?page=1"', r'href="#"', content)
            
            if content != original_content:
                with open(catalog_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.fixes_applied.append("catalog.html: shop.html Link entfernt (ersetzt durch #)")
                
    def fix_webton_images(self):
        """Prüft und korrigiert Webton-Bilder"""
        webton_dir = self.root_dir / "assets/img/webton"
        
        if not webton_dir.exists():
            self.issues_found.append("Webton-Bildverzeichnis fehlt: assets/img/webton")
            return
            
        # Prüfe auf fehlende Bilder
        required_images = [
            "hero/default-banner.gif",
            "about/ab1.webp",
            "about/<EMAIL>", 
            "about/ab2.webp",
            "about/<EMAIL>",
            "about/ab3.webp",
            "about/<EMAIL>"
        ]
        
        for img_path in required_images:
            full_path = webton_dir / img_path
            if not full_path.exists():
                self.issues_found.append(f"Fehlendes Bild: assets/img/webton/{img_path}")
                
    def run_fixes(self):
        """Führt alle Korrekturen durch"""
        print("Starte automatische Link-Korrekturen...")
        
        self.fix_shop_product_links()
        self.fix_missing_logo_file()
        self.fix_hash_links()
        self.fix_tel_links()
        self.create_missing_shop_page()
        self.fix_webton_images()
        
        return self.generate_report()
        
    def generate_report(self):
        """Generiert einen Bericht über die durchgeführten Korrekturen"""
        report = []
        report.append("# Link-Korrektur Bericht - IsoPur Website")
        report.append("")
        report.append(f"**Durchgeführte Korrekturen:** {len(self.fixes_applied)}")
        report.append(f"**Verbleibende Probleme:** {len(self.issues_found)}")
        report.append("")
        
        if self.fixes_applied:
            report.append("## ✅ Erfolgreich korrigiert")
            report.append("")
            for fix in self.fixes_applied:
                report.append(f"- {fix}")
            report.append("")
            
        if self.issues_found:
            report.append("## ⚠️ Verbleibende Probleme")
            report.append("")
            for issue in self.issues_found:
                report.append(f"- {issue}")
            report.append("")
            
        report.append("## 📋 Nächste Schritte")
        report.append("")
        report.append("1. **Webton-Bilder hinzufügen**: Die fehlenden Bilder im webton-Verzeichnis müssen hinzugefügt werden")
        report.append("2. **Externe Links prüfen**: Die Strapi-Links sollten auf Verfügbarkeit geprüft werden")
        report.append("3. **Link-Checker anpassen**: tel: und mailto: Links sollten vom Checker ignoriert werden")
        report.append("4. **Finale Validierung**: Nach den Korrekturen sollte eine erneute Link-Prüfung durchgeführt werden")
        
        return "\n".join(report)

if __name__ == "__main__":
    fixer = LinkFixer()
    report = fixer.run_fixes()
    
    # Speichere Bericht
    with open('link_fixes_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
        
    print("Korrekturen abgeschlossen. Bericht gespeichert in: link_fixes_report.md")
    print("\n" + "="*50)
    print(report)
