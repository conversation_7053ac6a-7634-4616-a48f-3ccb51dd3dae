/**
 * IsoPur Product Search & Filter System
 * Version: 1.0
 * Author: IsoPur Team
 */

class ProductSearch {
    constructor() {
        this.products = [];
        this.categories = [];
        this.filteredProducts = [];
        this.currentCategory = 'all';
        this.currentSearchTerm = '';
        
        this.init();
    }

    async init() {
        try {
            await this.loadProductData();
            this.setupEventListeners();
            this.renderProducts();
        } catch (error) {
            console.error('Error initializing product search:', error);
        }
    }

    async loadProductData() {
        try {
            // Try to load from relative path first
            let response;
            try {
                response = await fetch('./data.json');
            } catch (e) {
                // Fallback to absolute path
                response = await fetch('/products/data.json');
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            this.products = data.products || [];
            this.categories = Object.keys(data.categories || {});
            this.filteredProducts = [...this.products];

            console.log(`Loaded ${this.products.length} products and ${this.categories.length} categories`);
        } catch (error) {
            console.error('Error loading product data:', error);
            // Fallback data for local testing
            this.products = [
                {
                    slug: 'aquamat',
                    name: 'AQUAMAT',
                    description: 'Starre Dichtungsschlämme auf Zementbasis.',
                    category: 'Bauwerksabdichtung',
                    local_image: '../assets/img/products/AQUAMAT.png'
                },
                {
                    slug: 'flex-pu-2k',
                    name: 'FLEX PU-2K',
                    description: 'Zweikomponentige PU-Fugendichtmasse.',
                    category: 'Bauwerksabdichtung',
                    local_image: '../assets/img/products/FLEX-PU-2K.png'
                },
                {
                    slug: 'isoflex-pas-580',
                    name: 'ISOFLEX-PAS 580',
                    description: 'Zweikomponentige Polyaspartic-Flüssigabdichtung.',
                    category: 'Bauwerksabdichtung',
                    local_image: '../assets/img/products/ISOFLEX-PAS-580.png'
                },
                {
                    slug: 'topcoat-pu-710',
                    name: 'TOPCOAT-PU 710',
                    description: 'Elastischer, UV-stabiler, einkomponentiger Schutzanstrich auf Polyurethanbasis.',
                    category: 'Farben & Oberflächenschutz',
                    local_image: '../assets/img/products/TOPCOAT-PU-710.png'
                },
                {
                    slug: 'aquamat-monoelastic-ultra',
                    name: 'AQUAMAT-MONOELASTIC ULTRA',
                    description: 'Flexible, schnellabbindende, rissüberbrückende, einkomponentige Dichtungsschlämme auf Zementbasis.',
                    category: 'Bauwerksabdichtung',
                    local_image: '../assets/img/products/AQUAMAT-MONOELASTIC-ULTRA.png'
                }
            ];
            this.filteredProducts = [...this.products];
            console.log('Using fallback product data for local testing');
        }
    }

    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('product-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentSearchTerm = e.target.value.toLowerCase();
                this.filterProducts();
            });
        }

        // Category filter
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.currentCategory = e.target.value;
                this.filterProducts();
            });
        }

        // Clear filters button
        const clearButton = document.getElementById('clear-filters');
        if (clearButton) {
            clearButton.addEventListener('click', () => {
                this.clearFilters();
            });
        }
    }

    filterProducts() {
        this.filteredProducts = this.products.filter(product => {
            const matchesSearch = this.currentSearchTerm === '' || 
                product.name.toLowerCase().includes(this.currentSearchTerm) ||
                product.description.toLowerCase().includes(this.currentSearchTerm) ||
                product.category.toLowerCase().includes(this.currentSearchTerm);

            const matchesCategory = this.currentCategory === 'all' || 
                product.category === this.currentCategory;

            return matchesSearch && matchesCategory;
        });

        this.renderProducts();
        this.updateResultsCount();
    }

    clearFilters() {
        this.currentSearchTerm = '';
        this.currentCategory = 'all';
        
        const searchInput = document.getElementById('product-search');
        const categoryFilter = document.getElementById('category-filter');
        
        if (searchInput) searchInput.value = '';
        if (categoryFilter) categoryFilter.value = 'all';
        
        this.filteredProducts = [...this.products];
        this.renderProducts();
        this.updateResultsCount();
    }

    renderProducts() {
        const container = document.getElementById('products-container');
        if (!container) return;

        if (this.filteredProducts.length === 0) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="text-center py-5">
                        <div class="icon btn btn-circle btn-lg btn-soft-primary pe-none mx-auto mb-4">
                            <i class="uil uil-search"></i>
                        </div>
                        <h4>Keine Produkte gefunden</h4>
                        <p class="text-muted">Versuchen Sie andere Suchbegriffe oder Filter.</p>
                        <button class="btn btn-primary rounded-pill" onclick="productSearch.clearFilters()">
                            Filter zurücksetzen
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        const productsHTML = this.filteredProducts.map(product => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card shadow-lg h-100">
                    <figure class="card-img-top">
                        <img src="${product.local_image || '../assets/img/products/placeholder.png'}" 
                             alt="${product.name}" 
                             class="img-fluid" 
                             style="height: 200px; object-fit: contain; width: 100%;" />
                    </figure>
                    <div class="card-body d-flex flex-column">
                        <h4 class="card-title mb-3">${product.name}</h4>
                        <p class="card-text">${product.description}</p>
                        <div class="mt-auto">
                            <span class="badge bg-soft-primary text-primary mb-2">${product.category}</span>
                            <br>
                            <a href="${product.slug}.html" class="btn btn-primary rounded-pill">
                                Details ansehen
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = productsHTML;
    }

    updateResultsCount() {
        const counter = document.getElementById('results-count');
        if (counter) {
            const total = this.products.length;
            const filtered = this.filteredProducts.length;
            counter.textContent = `${filtered} von ${total} Produkten`;
        }
    }

    // Public method to get product by slug
    getProduct(slug) {
        return this.products.find(product => product.slug === slug);
    }

    // Public method to get products by category
    getProductsByCategory(category) {
        return this.products.filter(product => product.category === category);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.productSearch = new ProductSearch();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductSearch;
}
