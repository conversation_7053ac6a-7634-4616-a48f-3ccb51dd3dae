#!/usr/bin/env python3
"""
Update all product page links from index.html to search.html
"""

import os
import re
import glob
from pathlib import Path

def update_product_links():
    """Update all references from products/index.html to products/search.html"""
    
    # Find all HTML files in products directory
    products_dir = Path("products")
    html_files = list(products_dir.glob("*.html"))
    
    updates_made = []
    
    for html_file in html_files:
        if html_file.name == "search.html":
            continue  # Skip the search.html file itself
            
        print(f"Updating: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update breadcrumb links
        content = re.sub(
            r'<li class="breadcrumb-item"><a href="index\.html">Produkte</a></li>',
            r'<li class="breadcrumb-item"><a href="search.html">Produkte</a></li>',
            content
        )
        
        # Update navigation dropdown links
        content = re.sub(
            r'<a class="dropdown-item" href="index\.html">Alle Kategorien</a>',
            r'<a class="dropdown-item" href="search.html">Produktkatalog & Suche</a>',
            content
        )
        
        content = re.sub(
            r'<a class="dropdown-item active" href="index\.html">Alle Kategorien</a>',
            r'<a class="dropdown-item" href="search.html">Produktkatalog & Suche</a>',
            content
        )
        
        # Update any other references to index.html in products context
        content = re.sub(
            r'href="index\.html"([^>]*>)([^<]*Kategorie|[^<]*Produkt)',
            r'href="search.html"\1\2',
            content
        )
        
        if content != original_content:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            updates_made.append(str(html_file))
            print(f"  ✅ Updated: {html_file}")
        else:
            print(f"  ⏭️  No changes needed: {html_file}")
    
    # Update sitemap.json
    sitemap_file = products_dir / "sitemap.json"
    if sitemap_file.exists():
        print(f"Updating: {sitemap_file}")
        
        with open(sitemap_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update URLs in sitemap
        content = re.sub(
            r'"/products/index\.html',
            r'"/products/search.html',
            content
        )
        
        if content != original_content:
            with open(sitemap_file, 'w', encoding='utf-8') as f:
                f.write(content)
            updates_made.append(str(sitemap_file))
            print(f"  ✅ Updated: {sitemap_file}")
    
    print(f"\n📊 Summary:")
    print(f"Files updated: {len(updates_made)}")
    for file in updates_made:
        print(f"  - {file}")
    
    if not updates_made:
        print("No files needed updating.")

if __name__ == "__main__":
    print("🔄 Updating product page links...")
    update_product_links()
    print("✅ Update complete!")
